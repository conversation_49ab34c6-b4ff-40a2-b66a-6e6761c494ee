<template>
  <view class="live-container">
    <view v-if="roomInfo && roomInfo.id">

      <view id="videoContainer" v-show="!showWaiting && !showUnlockContent"></view>

      <!-- 等待界面 -->
      <waiting-screen :visible="showWaiting"></waiting-screen>

      <!-- 解锁内容界面 -->
      <view v-if="showUnlockContent" class="unlock-content-wrapper">
        <view class="unlock-content">
          <view class="unlock-icon">🔒</view>
          <text class="unlock-title">{{unlockTitle}}</text>
          <text class="unlock-desc">{{unlockDesc}}</text>
          <view class="unlock-btn" @click="handleUnlock">
            <text>{{unlockButtonText}}</text>
          </view>
        </view>
      </view>

      <!-- 付费确认弹窗 -->
      <view v-if="showPaymentPopup" class="popup-mask">
        <view class="popup-content">
          <text class="popup-title">{{$t('确认解锁')}}</text>
          <text class="popup-desc">
            {{$t('确定支付')}} {{ roomInfo.price || 0 }}金币 {{$t('解锁此内容吗？')}}
          </text>
          <view class="popup-btns">
            <view class="popup-btn cancel" @click.stop="cancelPayment"><text>{{$t('取消')}}</text></view>
            <view class="popup-btn confirm" @click.stop="confirmPayment"><text>{{$t('确认支付')}}</text></view>
          </view>
        </view>
      </view>

      <view class="mute-btn" v-show="!showWaiting && !showUnlockContent"
            :style="{top: `${this.statusBarHeight >0 ? (this.statusBarHeight+85) : 85}px`}"
      >
        <mute-btn :status-bar-height="statusBarHeight" :is-muted="videoElement ? videoElement.muted : true"
                  @mute-change="handleMuteChange"></mute-btn>
      </view>

      <!-- 顶部导航栏 -->
      <top-bar :status-bar-height="statusBarHeight" :room-info="roomInfo" :is-push="false"></top-bar>

      <!--  聊天框  -->
      <live-chat page-type="player" :room-info="roomInfo" :is-push="false"></live-chat>

      <!--  礼物面板  -->
      <live-gift :room-id="roomInfo.id" :room-user-id="roomInfo.userId"></live-gift>

      <!--   礼物墙、礼物统计   -->
      <stats-panel :status-bar-height="statusBarHeight" :room-info="roomInfo"></stats-panel>

      <!-- 点赞动画   -->
      <like-animation></like-animation>

      <!-- 观看用户列表弹窗 -->
      <viewers-list ref="viewersList" :room-id="roomInfo.id"></viewers-list>
    </view>

  </view>
</template>

<script>
import LiveChat from '@/components/live/LiveChat'
import LiveGift from '@/components/live/LiveGift'
import {createVideo} from '@/components/live/js/live.tool.js'
import {
  AppsLiveApiLiveRoomsApiOfflineSocketOn,
  AppsLiveApiLiveRoomsApiOfflineSocketOff,
  AppsLiveApiLiveRoomsApiStartSocketOn,
  AppsLiveApiLiveRoomsApiStartSocketOff, AppsLiveApiLiveRoomsApiDetail
} from '@/network/api/live-rooms.ts'
import {AppsLiveApiLiveBuyContentApiSave} from '@/network/api/live-buy-content';
import {Socket} from "@/Lib/Socket";
import i18n from "@/Lib/i18n";
import TopBar from "@/components/live/TopBar";
import StatsPanel from "@/components/live/StatsPanel";
import {Config} from "@/Config";
import MuteBtn from "@/components/live/MuteBtn";
import WaitingScreen from '@/components/live/WaitingScreen';
import LikeAnimation from "@/components/live/LikeAnimation.vue";
import ViewersList from "@/components/live/ViewersList.vue";
import {User} from "@/Lib/User";

export default {
  components: {
    LikeAnimation,
    MuteBtn,
    StatsPanel,
    TopBar,
    LiveChat,
    LiveGift,
    WaitingScreen,
    ViewersList
  },
  data() {
    return {
      roomInfo: {},
      videoElement: null,
      showWaiting: true,  // 默认显示等待界面
      showUnlockContent: false, // 显示解锁内容界面
      showPaymentPopup: false, // 显示付费确认弹窗
      statusBarHeight: 0,
    }
  },
  computed: {
    unlockTitle() {
      if (this.roomInfo.paymentType === 2) {
        return this.$t('会员专享内容');
      } else if (this.roomInfo.paymentType === 3) {
        return this.$t('付费内容');
      }
      return this.$t('解锁内容');
    },
    unlockDesc() {
      if (this.roomInfo.paymentType === 2) {
        return this.$t('此内容为会员专享，请开通会员后观看');
      } else if (this.roomInfo.paymentType === 3) {
        return this.$t('此内容需要付费解锁，支付') + ` ${this.roomInfo.price || 0}` + this.$t('金币即可观看');
      }
      return this.$t('请解锁后观看此内容');
    },
    unlockButtonText() {
      if (this.roomInfo.paymentType === 2) {
        return this.$t('开通会员');
      } else if (this.roomInfo.paymentType === 3) {
        return this.$t('立即解锁');
      }
      return this.$t('解锁');
    }
  },
  onLoad(opt) {
    this.roomId = parseInt(opt.roomId) || 0;
    const token = opt.token;
    const userId = opt.userId;
    this.statusBarHeight = parseInt(opt.statusBarHeight);
    console.log('token:', token);
    console.log('roomId:', this.roomId);
    console.log('userId:', userId);
    if (!this.roomId || !token) {
      uni.showModal({
        title: this.$t('提示'),
        content: this.$t('请选择直播间'),
        showCancel: false,
        confirmText: this.$t('确定'),
        success: (res) => {
          if (res.confirm) {
            uni.navigateBack();
          }
        }
      })
      return;
    }

    User.setLongToken(token);
    User.setUserId(userId);

    // 监听开播事件
    AppsLiveApiLiveRoomsApiStartSocketOn(true, this.onLiveStart);

    // 监听下播事件
    AppsLiveApiLiveRoomsApiOfflineSocketOn(true, this.onLiveOff);

    // 监听打开观看用户列表事件
    uni.$on('openViewersList', this.handleOpenViewersList);

    // 创建连接
    Socket.getInstance().connect({
      roomId: this.roomId
    })
  },
  onReady() {
    this.preventBounce();
    this.fetchRoomSettings();
  },
  onShow() {
    Socket.getInstance().ping()
  },
  onUnload() {
    // 清理所有监听器
    AppsLiveApiLiveRoomsApiStartSocketOff(this.onLiveStart);
    AppsLiveApiLiveRoomsApiOfflineSocketOff(this.onLiveOff);
    uni.$off('openViewersList', this.handleOpenViewersList);
    Socket.getInstance().close();
    // 关闭当前连接
    this.closeCurrentConnection();
  },
  methods: {
    $t(text) {
      return i18n.t(text);
    },
    handleMuteChange(muted) {
      console.log('muted:', muted)
      this.videoElement.muted = muted
    },
    onLiveStart(res) {
      console.log('开播事件:', res);
      if (res && res.status === 2) {
        // 隐藏等待界面
        this.showWaiting = false;
        // 如果有URL则开始播放
        this.$nextTick(() => {
          this.playWHEPStream();
        });
      } else {
        // 其他状态显示等待界面
        this.showWaiting = true;
        // 关闭当前连接
        this.closeCurrentConnection();
      }
    },
    onLiveOff(res) {
      if (res && res.status === 3) {
        // 显示等待界面
        this.showWaiting = true;
        // 关闭当前连接
        this.closeCurrentConnection();
      }
    },
    generateUrl() {
      const roomId = this.roomInfo.id;
      const display = this.roomInfo.userId;
      if (!roomId || !display) {
        uni.showModal({
          title: this.$t('请检查直播间ID'),
          content: this.$t('直播间ID无效，请检查直播间链接'),
          showCancel: false,
        })
        return '';
      }
      return Config.LIVE_PLAY_HOST + `?app=r${roomId}&stream=d${display}.flv`;
    },
    // 禁止页面滚动回弹
    preventBounce() {
      // 阻止整个文档的触摸滑动默认行为
      document.addEventListener('touchmove', function (e) {
        e.preventDefault();
      }, {passive: false});

      // 对于iOS设备的特殊处理
      if (/iphone|ipad|ipod/i.test(navigator.userAgent.toLowerCase())) {
        // 设置body和html样式
        document.body.style.overflow = 'hidden';
        document.body.style.position = 'fixed';
        document.body.style.width = '100%';
        document.body.style.height = '100%';
        document.documentElement.style.overflow = 'hidden';
      }
    },
    // 获取直播间设置
    fetchRoomSettings() {
      if (!this.roomId) return;
      AppsLiveApiLiveRoomsApiDetail({
        id: this.roomId
      }).then(res => {
        console.log('获取房间设置成功:', res);
        if (res) {
          this.roomInfo = res;

          // 根据paymentType处理不同逻辑
          if (res.paymentType === 1) {
            // paymentType=1，保持原逻辑
            if (res.status === 2) {
              this.$nextTick(() => {
                this.playWHEPStream();
              })
            }
          } else {
            // paymentType!=1，隐藏等待界面，显示解锁内容，不执行playWHEPStream
            this.showWaiting = false;
            this.showUnlockContent = true;
          }
        }
      }).catch(err => {
        console.error('获取房间设置失败:', err);
        uni.showToast({
          title: this.$t('获取设置失败'),
          icon: 'none'
        });
      });
    },

    // 关闭当前连接
    closeCurrentConnection() {
      if (this.currentPeerConnection) {
        try {
          this.currentPeerConnection.close();
        } catch (e) {
          console.error('关闭链接失败', e);
        }
        this.currentPeerConnection = null;
      }

      // 移除视频元素
      if (this.videoElement) {
        const container = document.getElementById('videoContainer');
        if (container && container.contains(this.videoElement)) {
          container.removeChild(this.videoElement);
        }
        this.videoElement = null;
      }
    },

    // WHEP 流播放器
    async playWHEPStream() {
      // 如果已经在连接中，不要重复连接
      if (this.isConnecting) {
        console.log("已经在连接中，不要重复连接");
        return;
      }

      this.isConnecting = true;
      // 关闭当前连接
      this.closeCurrentConnection();

      // 创建video元素
      const container = document.getElementById('videoContainer');
      if (!container) {
        console.error('找不到视频容器');
        return;
      }

      // 创建新的video元素
      this.videoElement = createVideo('videoContainer');
      if (!this.videoElement) {
        console.error('创建视频元素失败');
        return;
      }

      try {
        // 创建 RTCPeerConnection
        const pc = new RTCPeerConnection({
          iceServers: [{urls: 'stun:stun.l.google.com:19302'}]
        });

        this.currentPeerConnection = pc;

        // 监听ICE连接状态变化
        pc.oniceconnectionstatechange = () => {
          console.log('ICE连接状态:', pc.iceConnectionState);
          if (pc.iceConnectionState === 'failed' || pc.iceConnectionState === 'disconnected') {
            console.error('ICE连接失败或断开');
          }
        };

        // 监听连接状态变化
        pc.onconnectionstatechange = () => {
          console.log('连接状态:', pc.connectionState);
          if (pc.connectionState === 'failed' || pc.connectionState === 'disconnected') {
            console.error('连接失败或断开');
          }
        };

        // 监听远程流
        pc.ontrack = (event) => {
          if (event.streams && event.streams[0]) {
            this.videoElement.srcObject = event.streams[0];

            // 尝试播放
            this.videoElement.play().then(() => {
              // 播放成功
              this.reconnectCount = 0;
              // 隐藏等待界面
              this.showWaiting = false;
            }).catch(err => {
              console.error('自动播放失败:', err);
            });
          }
        };

        // 设置接收音视频轨道
        pc.addTransceiver('video', {direction: 'recvonly'});
        pc.addTransceiver('audio', {direction: 'recvonly'});

        // 创建并设置本地描述
        const offer = await pc.createOffer();
        await pc.setLocalDescription(offer);

        const whepUrl = this.generateUrl()
        console.log(whepUrl)
        // 发送 Offer 到 WHEP 服务器
        const response = await fetch(whepUrl, {
          method: 'POST',
          headers: {'Content-Type': 'application/sdp'},
          body: offer.sdp
        });

        if (!response.ok) {
          console.log(`播放失败: ${response.status} ${response.statusText}`);
          // 显示等待界面
          this.showWaiting = true;
          uni.showModal({
            title: this.$t('播放失败'),
            showCancel: false,
            confirmText: this.$t('确定'),
          })
          return;
        }

        // 设置远程描述
        const answerSdp = await response.text();
        await pc.setRemoteDescription(new RTCSessionDescription({
          type: 'answer',
          sdp: answerSdp
        }));

        // 连接成功，重置重连计数
        this.reconnectCount = 0;
        this.isConnecting = false;

      } catch (error) {
        console.error('WHEP 连接失败:', error);
        // 显示等待界面
        this.showWaiting = true;
      }
    },

    /**
     * 处理打开观看用户列表事件
     */
    handleOpenViewersList(roomId) {
      if (this.$refs.viewersList && roomId) {
        this.$refs.viewersList.open();
      }
    },

    // 处理解锁操作
    async handleUnlock() {
      // 检查用户是否已登录
      const userId = User.getUserId();
      if (!userId) {
        console.log('用户未登录，需要先登录');
        uni.showToast({
          title: this.$t('请先登录'),
          icon: 'none'
        });

        // 获取当前页面路径作为返回地址
        const pages = getCurrentPages();
        const currentPage = pages[pages.length - 1];
        const retUrl = currentPage ? encodeURIComponent(currentPage.route) : '';

        // 跳转到登录页面
        uni.navigateTo({
          url: `/pages/login/index?retUrl=${retUrl}`
        });
        return;
      }

      if (this.roomInfo.paymentType === 2) {
        // 跳转到会员页面
        uni.navigateTo({
          url: '/pages/membership/index'
        });
      } else if (this.roomInfo.paymentType === 3) {
        // 显示付费确认弹窗
        this.showPaymentPopup = true;
      }
    },

    // 确认付费解锁
    async confirmPayment() {
      // 再次检查用户是否已登录
      const userId = User.getUserId();
      if (!userId) {
        console.log('用户未登录，需要先登录');
        uni.showToast({
          title: this.$t('请先登录'),
          icon: 'none'
        });

        // 获取当前页面路径作为返回地址
        const pages = getCurrentPages();
        const currentPage = pages[pages.length - 1];
        const retUrl = currentPage ? encodeURIComponent(currentPage.route) : '';

        // 跳转到登录页面
        uni.navigateTo({
          url: `/pages/login/index?retUrl=${retUrl}`
        });
        this.showPaymentPopup = false;
        return;
      }

      try {
        const params = {
          targetTable: 'live_rooms',
          targetId: this.roomInfo.id
        };
        const res = await AppsLiveApiLiveBuyContentApiSave(params);
        if (res && res.success === true) {
          // 付费成功后，隐藏解锁界面，开始播放
          this.showPaymentPopup = false;
          this.showUnlockContent = false;
          uni.showToast({
            title: this.$t('解锁成功'),
            icon: 'success'
          });
          
          // 开始播放直播
          if (this.roomInfo.status === 2) {
            this.$nextTick(() => {
              this.playWHEPStream();
            })
          } else {
            this.showWaiting = true;
          }
        }
      } catch (error) {
        console.error('付费解锁失败', error);
        uni.showToast({
          title: this.$t('解锁失败'),
          icon: 'none'
        });
      }
    },

    // 取消付费解锁
    cancelPayment() {
      this.showPaymentPopup = false;
    },

  }
}
</script>

<style scoped lang="scss">
/* 禁止页面回弹样式 */
page {
  overflow: hidden;
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background-color: #2a2a2a;
}

#videoContainer {
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mute-btn {
  position: fixed;
  right: 30rpx;
  top: 150rpx;
}

/* 解锁内容界面样式 */
.unlock-content-wrapper {
  position: absolute;
  z-index: 99;
  left: 45rpx;
  top: 20%;

  .unlock-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 660rpx;
    padding: 60rpx 40rpx;
    background-color: rgba(0, 0, 0, 0.8);
    border-radius: 20rpx;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-sizing: border-box;

    .unlock-icon {
      font-size: 120rpx;
      margin-bottom: 40rpx;
    }

    .unlock-title {
      display: block;
      font-size: 36rpx;
      font-weight: bold;
      color: #ffffff;
      margin-bottom: 20rpx;
    }

    .unlock-desc {
      display: block;
      font-size: 28rpx;
      color: #cccccc;
      margin-bottom: 60rpx;
      line-height: 1.5;
      max-width: 500rpx;
    }

    .unlock-btn {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: #ffffff;
      padding: 24rpx 60rpx;
      border-radius: 50rpx;
      font-size: 32rpx;
      font-weight: bold;
      display: inline-block;
      box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);
      transition: all 0.3s ease;

      &:active {
        transform: translateY(2rpx);
        box-shadow: 0 4rpx 10rpx rgba(102, 126, 234, 0.3);
      }

      text {
        color: #ffffff;
      }
    }
  }
}

/* 付费确认弹窗样式 */
.popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;

  .popup-content {
    width: 80%;
    max-width: 600rpx;
    background-color: #ffffff;
    border-radius: 20rpx;
    padding: 60rpx 40rpx 40rpx;
    box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.3);

    .popup-title {
      font-size: 36rpx;
      font-weight: bold;
      text-align: center;
      margin-bottom: 30rpx;
      color: #333333;
      display: block;
    }

    .popup-desc {
      font-size: 28rpx;
      color: #666666;
      text-align: center;
      margin-bottom: 60rpx;
      line-height: 1.5;
      display: block;
    }

    .popup-btns {
      display: flex;
      justify-content: space-between;
      gap: 20rpx;

      .popup-btn {
        flex: 1;
        padding: 24rpx 0;
        border-radius: 50rpx;
        font-size: 30rpx;
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;

        &.cancel {
          background-color: #f5f5f5;

          text {
            color: #999999;
          }

          &:active {
            background-color: #eeeeee;
          }
        }

        &.confirm {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);

          text {
            color: #ffffff;
          }

          &:active {
            transform: translateY(2rpx);
            box-shadow: 0 4rpx 10rpx rgba(102, 126, 234, 0.3);
          }
        }
      }
    }
  }
}
</style>
